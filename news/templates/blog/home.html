{% extends "blog/base_blog.html" %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block blog_content %}
<!-- Hero Section with Ad -->
<div class="hero-section mb-5">
    <div class="position-relative">
        <h1 class="display-4 mb-4">💡 The Idea Engine</h1>
        <p class="lead mb-4">Your source for creative inspiration, innovation insights, and actionable business tips.</p>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <p class="mb-3">
                    <span class="badge bg-light text-dark me-2">✨ Creative Ideas</span>
                    <span class="badge bg-light text-dark me-2">🚀 Innovation Tips</span>
                    <span class="badge bg-light text-dark">💼 Business Insights</span>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Top Banner Ad -->
<div class="ad-container mb-5">
    <div class="ad-label">Advertisement</div>
    <!-- Google AdSense Banner Ad -->
    <ins class="adsbygoogle"
         style="display:block"
         data-ad-client="ca-pub-YOUR_ADSENSE_ID"
         data-ad-slot="BANNER_AD_SLOT"
         data-ad-format="auto"
         data-full-width-responsive="true"></ins>
    <script>
         (adsbygoogle = window.adsbygoogle || []).push({});
    </script>
</div>

<!-- Featured Articles -->
{% if featured_articles %}
<section class="mb-5">
    <h2 class="mb-4">🌟 Featured Articles</h2>
    <div class="row">
        {% for article in featured_articles %}
        <div class="col-md-4 mb-4">
            <div class="card h-100 feature-card">
                {% if article.featured_image %}
                <img src="{{ article.featured_image }}" class="card-img-top" alt="{{ article.title }}" style="height: 200px; object-fit: cover;">
                {% endif %}
                <div class="card-body">
                    <span class="badge bg-primary mb-2">{{ article.category.name }}</span>
                    <h5 class="card-title">{{ article.title }}</h5>
                    <p class="card-text">{{ article.excerpt }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{ article.reading_time }} min read</small>
                        <small class="text-muted">{{ article.views }} views</small>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{{ article.get_absolute_url }}" class="btn btn-outline-primary btn-sm">Read More</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}

<!-- Middle Ad -->
<div class="ad-container mb-5">
    <div class="ad-label">Advertisement</div>
    <!-- Google AdSense Middle Ad -->
    <ins class="adsbygoogle"
         style="display:block"
         data-ad-client="ca-pub-YOUR_ADSENSE_ID"
         data-ad-slot="MIDDLE_AD_SLOT"
         data-ad-format="auto"
         data-full-width-responsive="true"></ins>
    <script>
         (adsbygoogle = window.adsbygoogle || []).push({});
    </script>
</div>

<!-- Recent Articles -->
<section class="mb-5">
    <h2 class="mb-4">📚 Latest Articles</h2>
    <div class="row">
        {% for article in recent_articles %}
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <span class="badge bg-secondary mb-2">{{ article.category.name }}</span>
                    <h5 class="card-title">{{ article.title }}</h5>
                    <p class="card-text">{{ article.excerpt|truncatewords:20 }}</p>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <small class="text-muted">{{ article.published_at|date:"M d, Y" }}</small>
                        <small class="text-muted">{{ article.reading_time }} min read</small>
                    </div>
                    <a href="{{ article.get_absolute_url }}" class="btn btn-primary btn-sm">Read Article</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <div class="text-center">
        <a href="{% url 'blog:article_list' %}" class="btn btn-outline-primary">View All Articles</a>
    </div>
</section>

<!-- Categories Section -->
{% if categories %}
<section class="mb-5">
    <h2 class="mb-4">🗂️ Explore Categories</h2>
    <div class="row">
        {% for category in categories %}
        <div class="col-md-3 col-sm-6 mb-3">
            <a href="{{ category.get_absolute_url }}" class="text-decoration-none">
                <div class="card text-center h-100">
                    <div class="card-body">
                        <h6 class="card-title">{{ category.name }}</h6>
                        <small class="text-muted">{{ category.articles.count }} articles</small>
                    </div>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}

<!-- Bottom Ad -->
<div class="ad-container mb-5">
    <div class="ad-label">Advertisement</div>
    <!-- Google AdSense Bottom Ad -->
    <ins class="adsbygoogle"
         style="display:block"
         data-ad-client="ca-pub-YOUR_ADSENSE_ID"
         data-ad-slot="BOTTOM_AD_SLOT"
         data-ad-format="auto"
         data-full-width-responsive="true"></ins>
    <script>
         (adsbygoogle = window.adsbygoogle || []).push({});
    </script>
</div>

<!-- Newsletter CTA -->
<section class="text-center py-5" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); border-radius: 15px;">
    <h3>💡 Get Ideas Delivered to Your Inbox</h3>
    <p class="lead">Join thousands of creative professionals getting fresh inspiration weekly.</p>
    <a href="{% url 'subscribers:subscribe' %}" class="btn btn-lg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white;">
        <i class="fas fa-rocket me-2"></i>Start My Idea Journey
    </a>
</section>
{% endblock %}

{% block sidebar %}
{{ block.super }}

<!-- Additional sidebar content for home page -->
<div class="card mb-4">
    <div class="card-header">
        <h5>💰 Monetization Stats</h5>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-6">
                <div class="h4 text-primary">{{ total_views|default:0 }}</div>
                <small class="text-muted">Total Views</small>
            </div>
            <div class="col-6">
                <div class="h4 text-success">${{ estimated_revenue|default:0 }}</div>
                <small class="text-muted">Est. Revenue</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
