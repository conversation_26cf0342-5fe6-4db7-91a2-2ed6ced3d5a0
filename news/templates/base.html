<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}The Idea Engine{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    {% load static %}
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="main-wrapper">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="{% url 'newsletter:home' %}">
                <i class="fas fa-lightbulb"></i> The Idea Engine
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'newsletter:home' %}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'subscribers:subscribe' %}">Subscribe</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'subscribers:unsubscribe' %}">Unsubscribe</a>
                    </li>
                    {% if user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'newsletter:list' %}">Newsletters</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'newsletter:create' %}">Create Newsletter</a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            {% if user.is_staff %}
                            <li><a class="dropdown-item" href="/admin/">Admin Panel</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{% url 'admin:logout' %}">Logout</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/login/">Login</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="py-5 mt-5" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-3">
                        <i class="fas fa-lightbulb me-2"></i>The Idea Engine
                    </h5>
                    <p class="text-muted mb-0">&copy; 2024 The Idea Engine. Fueling creativity worldwide.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex flex-column flex-md-row justify-content-md-end gap-3">
                        <a href="{% url 'subscribers:subscribe' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Subscribe
                        </a>
                        <a href="{% url 'subscribers:unsubscribe' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-minus me-1"></i>Unsubscribe
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
