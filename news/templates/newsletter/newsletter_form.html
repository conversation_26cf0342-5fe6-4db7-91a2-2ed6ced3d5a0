{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - Newsletter App{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-edit"></i> {{ title }}</h1>
            <a href="{% url 'newsletter:list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>

        <div class="card shadow">
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ form|crispy }}
                </form>
            </div>
        </div>

        {% if newsletter %}
        <div class="mt-3">
            <div class="btn-group" role="group">
                <a href="{% url 'newsletter:detail' newsletter.pk %}" class="btn btn-outline-info">
                    <i class="fas fa-eye"></i> View Details
                </a>
                <a href="{% url 'newsletter:preview' newsletter.pk %}" class="btn btn-outline-success">
                    <i class="fas fa-search"></i> Preview
                </a>
                {% if not newsletter.is_sent %}
                <form method="post" action="{% url 'newsletter:send' newsletter.pk %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-warning" 
                            onclick="return confirm('Are you sure you want to send this newsletter to all subscribers?')">
                        <i class="fas fa-paper-plane"></i> Send Now
                    </button>
                </form>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Auto-save functionality (optional)
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            // You could implement auto-save here
            console.log('Content changed');
        });
    });
});
</script>
{% endblock %}
