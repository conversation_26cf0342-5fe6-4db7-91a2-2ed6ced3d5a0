# Generated by Django 5.1.4 on 2025-06-17 05:00

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Subscriber',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('first_name', models.CharField(blank=True, max_length=100)),
                ('last_name', models.CharField(blank=True, max_length=100)),
                ('is_active', models.BooleanField(default=False)),
                ('is_confirmed', models.Boolean<PERSON>ield(default=False)),
                ('confirmation_token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('date_subscribed', models.DateTimeField(auto_now_add=True)),
                ('date_confirmed', models.DateTime<PERSON>ield(blank=True, null=True)),
                ('date_unsubscribed', models.DateTime<PERSON>ield(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Subscriber',
                'verbose_name_plural': 'Subscribers',
                'ordering': ['-date_subscribed'],
            },
        ),
    ]
