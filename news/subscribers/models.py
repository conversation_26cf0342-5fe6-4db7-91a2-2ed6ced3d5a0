import uuid
from django.db import models
from django.utils import timezone
from datetime import timedelta
from django.conf import settings


class Subscriber(models.Model):
    """Model for newsletter subscribers"""
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=100, blank=True)
    last_name = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=False)
    is_confirmed = models.BooleanField(default=False)
    confirmation_token = models.UUIDField(default=uuid.uuid4, editable=False)
    date_subscribed = models.DateTimeField(auto_now_add=True)
    date_confirmed = models.DateTimeField(null=True, blank=True)
    date_unsubscribed = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-date_subscribed']
        verbose_name = 'Subscriber'
        verbose_name_plural = 'Subscribers'

    def __str__(self):
        return self.email

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def confirm_subscription(self):
        """Confirm the subscription"""
        self.is_confirmed = True
        self.is_active = True
        self.date_confirmed = timezone.now()
        self.save()

    def unsubscribe(self):
        """Unsubscribe the user"""
        self.is_active = False
        self.date_unsubscribed = timezone.now()
        self.save()

    def is_confirmation_expired(self):
        """Check if confirmation token is expired"""
        expiry_date = self.date_subscribed + timedelta(days=settings.NEWSLETTER_CONFIRMATION_DAYS)
        return timezone.now() > expiry_date
