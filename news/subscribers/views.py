from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.urls import reverse
from django.http import Http404
from .models import Subscriber
from .forms import SubscriptionForm, UnsubscribeForm
from newsletter.tasks import send_confirmation_email_task


def subscribe(request):
    """Handle newsletter subscription"""
    if request.method == 'POST':
        form = SubscriptionForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']

            # Check if subscriber already exists
            subscriber, created = Subscriber.objects.get_or_create(
                email=email,
                defaults={
                    'first_name': form.cleaned_data.get('first_name', ''),
                    'last_name': form.cleaned_data.get('last_name', ''),
                }
            )

            if created or not subscriber.is_confirmed:
                # Send confirmation email
                send_confirmation_email_task.delay(subscriber.id)
                messages.success(
                    request,
                    'Thank you for subscribing! Please check your email to confirm your subscription.'
                )
            else:
                messages.info(request, 'You are already subscribed to our newsletter.')

            return redirect('subscribers:subscription_success')
    else:
        form = SubscriptionForm()

    return render(request, 'subscribers/subscribe.html', {'form': form})


def subscription_success(request):
    """Display subscription success page"""
    return render(request, 'subscribers/subscription_success.html')


def confirm_subscription(request, token):
    """Confirm newsletter subscription"""
    try:
        subscriber = Subscriber.objects.get(confirmation_token=token)

        if subscriber.is_confirmed:
            messages.info(request, 'Your subscription is already confirmed.')
        elif subscriber.is_confirmation_expired():
            messages.error(request, 'Your confirmation link has expired. Please subscribe again.')
            return redirect('subscribers:subscribe')
        else:
            subscriber.confirm_subscription()
            messages.success(request, 'Thank you! Your subscription has been confirmed.')

        return render(request, 'subscribers/confirmation_success.html', {'subscriber': subscriber})

    except Subscriber.DoesNotExist:
        messages.error(request, 'Invalid confirmation link.')
        return redirect('subscribers:subscribe')


def unsubscribe_form(request):
    """Display unsubscribe form"""
    if request.method == 'POST':
        form = UnsubscribeForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                subscriber = Subscriber.objects.get(email=email, is_active=True)
                subscriber.unsubscribe()
                messages.success(request, 'You have been successfully unsubscribed.')
                return redirect('subscribers:unsubscribe_success')
            except Subscriber.DoesNotExist:
                messages.error(request, 'Email not found in our subscription list.')
    else:
        form = UnsubscribeForm()

    return render(request, 'subscribers/unsubscribe.html', {'form': form})


def unsubscribe_direct(request, token):
    """Direct unsubscribe via token (from email link)"""
    try:
        subscriber = Subscriber.objects.get(confirmation_token=token, is_active=True)
        subscriber.unsubscribe()
        messages.success(request, 'You have been successfully unsubscribed.')
        return render(request, 'subscribers/unsubscribe_success.html', {'subscriber': subscriber})
    except Subscriber.DoesNotExist:
        messages.error(request, 'Invalid unsubscribe link.')
        return redirect('subscribers:subscribe')


def unsubscribe_success(request):
    """Display unsubscribe success page"""
    return render(request, 'subscribers/unsubscribe_success.html')
