# Django Settings
SECRET_KEY=django-insecure-4)7c^x_ai4)nq)l)3w$rnlmrld4kw^9&k+00h_z-@k-2x81_(0
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Email Configuration (Console for now - will print emails to console)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=YOUR_APP_PASSWORD_HERE
DEFAULT_FROM_EMAIL=Newsletter App <<EMAIL>>

# Site Configuration
SITE_URL=http://localhost:8000

# Newsletter Settings (Production - require confirmation)
NEWSLETTER_REQUIRE_CONFIRMATION=True
