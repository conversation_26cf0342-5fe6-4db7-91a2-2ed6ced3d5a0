#!/usr/bin/env python
"""
Convenience script to run Celery worker for email processing
"""
import os
import sys
import subprocess

def main():
    """Run Celery worker"""
    print("🚀 Starting Celery Worker for Email Processing")
    print("=" * 50)
    print("This worker will process:")
    print("- Confirmation emails")
    print("- Welcome emails")
    print("- Newsletter sending")
    print("=" * 50)

    # Change to the project directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    # Check if Redis is available
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis connection successful")
    except Exception as e:
        print("❌ Redis connection failed!")
        print("Please install and start Redis:")
        print("  Ubuntu/Debian: sudo apt install redis-server")
        print("  macOS: brew install redis")
        print("  Windows: Download from https://redis.io/download")
        print(f"Error: {e}")
        return

    # Activate virtual environment and run celery
    if os.name == 'nt':  # Windows
        activate_script = 'venv\\Scripts\\activate.bat'
        command = f'{activate_script} && celery -A newsletter_project worker --loglevel=info --concurrency=2'
        subprocess.run(command, shell=True)
    else:  # Unix/Linux/macOS
        activate_script = 'source venv/bin/activate'
        command = f'{activate_script} && celery -A newsletter_project worker --loglevel=info --concurrency=2'
        subprocess.run(command, shell=True, executable='/bin/bash')

if __name__ == '__main__':
    main()
