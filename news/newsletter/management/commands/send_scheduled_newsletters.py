from django.core.management.base import BaseCommand
from django.utils import timezone
from newsletter.models import Newsletter
from newsletter.tasks import send_newsletter_task


class Command(BaseCommand):
    help = 'Send scheduled newsletters that are due'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show which newsletters would be sent without actually sending them',
        )

    def handle(self, *args, **options):
        now = timezone.now()
        
        # Find newsletters that are scheduled and due to be sent
        due_newsletters = Newsletter.objects.filter(
            is_scheduled=True,
            scheduled_date__lte=now,
            is_sent=False
        )
        
        if not due_newsletters.exists():
            self.stdout.write(
                self.style.SUCCESS('No scheduled newsletters are due to be sent.')
            )
            return
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No newsletters will actually be sent')
            )
            for newsletter in due_newsletters:
                self.stdout.write(
                    f'Would send: "{newsletter.title}" (scheduled for {newsletter.scheduled_date})'
                )
            return
        
        sent_count = 0
        for newsletter in due_newsletters:
            try:
                # Queue the newsletter for sending
                send_newsletter_task.delay(newsletter.id)
                sent_count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Queued newsletter "{newsletter.title}" for sending'
                    )
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to queue newsletter "{newsletter.title}": {str(e)}'
                    )
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully queued {sent_count} newsletters for sending'
            )
        )
