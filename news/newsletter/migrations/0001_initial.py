# Generated by Django 5.1.4 on 2025-06-17 05:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Newsletter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('subject', models.CharField(help_text='Email subject line', max_length=200)),
                ('content', models.TextField(help_text='Newsletter content (HTML allowed)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_scheduled', models.BooleanField(default=False)),
                ('scheduled_date', models.DateTimeField(blank=True, null=True)),
                ('is_sent', models.BooleanField(default=False)),
                ('sent_date', models.DateTimeField(blank=True, null=True)),
                ('total_recipients', models.PositiveIntegerField(default=0)),
                ('total_sent', models.PositiveIntegerField(default=0)),
                ('total_failed', models.PositiveIntegerField(default=0)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Newsletter',
                'verbose_name_plural': 'Newsletters',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NewsletterSendLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipient_email', models.EmailField(max_length=254)),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('sent', 'Sent'), ('failed', 'Failed'), ('bounced', 'Bounced')], default='sent', max_length=20)),
                ('error_message', models.TextField(blank=True)),
                ('newsletter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='send_logs', to='newsletter.newsletter')),
            ],
            options={
                'ordering': ['-sent_at'],
                'unique_together': {('newsletter', 'recipient_email')},
            },
        ),
    ]
