# Django Newsletter Application

A comprehensive Django-based newsletter application with subscription management, email confirmation, and scheduled newsletter sending capabilities.

## Features

- **User Subscription**: Easy email subscription with double opt-in confirmation
- **Admin Panel**: Full admin interface for managing subscribers and newsletters
- **Newsletter Creation**: Rich text newsletter creation and editing
- **Email Sending**: Bulk email sending to all active subscribers
- **Scheduling**: Schedule newsletters for future delivery
- **Background Tasks**: Uses Celery + Redis for asynchronous email processing
- **Responsive Design**: Mobile-friendly Bootstrap-based templates
- **Unsubscribe**: One-click unsubscribe functionality
- **Email Templates**: Professional HTML email templates
- **Statistics**: Track newsletter delivery statistics

## Technology Stack

- **Backend**: Django 5.1.4
- **Database**: PostgreSQL
- **Task Queue**: Celery + Redis
- **Frontend**: Bootstrap 5, HTML5, CSS3
- **Email**: Django's built-in email framework

## Project Structure

```
newsletter_project/
├── newsletter/              # Core newsletter app
│   ├── models.py           # Newsletter and SendLog models
│   ├── views.py            # Newsletter management views
│   ├── forms.py            # Newsletter forms
│   ├── admin.py            # Admin interface
│   ├── tasks.py            # Celery tasks for email sending
│   └── urls.py             # Newsletter URLs
├── subscribers/            # Subscriber management app
│   ├── models.py           # Subscriber model
│   ├── views.py            # Subscription views
│   ├── forms.py            # Subscription forms
│   ├── admin.py            # Subscriber admin
│   └── urls.py             # Subscriber URLs
├── templates/              # HTML templates
│   ├── base.html           # Base template
│   ├── newsletter/         # Newsletter templates
│   └── subscribers/        # Subscriber templates
├── static/                 # Static files (CSS, JS)
└── newsletter_project/     # Django project settings
    ├── settings.py         # Main settings
    ├── urls.py             # URL configuration
    └── celery.py           # Celery configuration
```

## Installation & Setup

### Prerequisites

- Python 3.8+
- PostgreSQL
- Redis (for Celery)

### 1. Clone and Setup Virtual Environment

```bash
# Navigate to the project directory
cd news

# Activate virtual environment (already created)
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Setup

```bash
# Create PostgreSQL database
sudo -u postgres psql
CREATE DATABASE newsletter_db;
CREATE USER newsletter_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE newsletter_db TO newsletter_user;
\q
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
nano .env
```

Update the following variables in `.env`:
- `SECRET_KEY`: Generate a new Django secret key
- `DB_PASSWORD`: Your PostgreSQL password
- `EMAIL_HOST_USER`: Your email address
- `EMAIL_HOST_PASSWORD`: Your email app password

### 4. Django Setup

```bash
# Run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic
```

### 5. Start Services

#### Terminal 1: Django Development Server
```bash
python manage.py runserver
```

#### Terminal 2: Celery Worker
```bash
celery -A newsletter_project worker --loglevel=info
```

#### Terminal 3: Celery Beat (for scheduled tasks)
```bash
celery -A newsletter_project beat --loglevel=info
```

## Usage

### For End Users

1. **Subscribe**: Visit `http://localhost:8000` to subscribe
2. **Confirm**: Check email and click confirmation link
3. **Unsubscribe**: Use the unsubscribe link in emails or visit `/unsubscribe/`

### For Administrators

1. **Admin Panel**: Visit `http://localhost:8000/admin/`
2. **Create Newsletter**: Go to Newsletters → Create Newsletter
3. **Manage Subscribers**: View and manage subscribers in admin
4. **Send Newsletter**: Use the admin interface or newsletter management pages

### API Endpoints

- `/` - Home page with subscription form
- `/subscribe/` - Subscription form
- `/confirm/<token>/` - Email confirmation
- `/unsubscribe/` - Unsubscribe form
- `/unsubscribe/<token>/` - Direct unsubscribe
- `/newsletters/` - Newsletter management (staff only)
- `/admin/` - Django admin panel

## Email Configuration

### Gmail Setup

1. Enable 2-factor authentication
2. Generate an app password
3. Use app password in `EMAIL_HOST_PASSWORD`

### Other Email Providers

Update `EMAIL_HOST` and `EMAIL_PORT` in `.env` file according to your provider.

## Deployment

### Production Settings

1. Set `DEBUG=False` in `.env`
2. Configure proper `ALLOWED_HOSTS`
3. Use a production database
4. Set up proper email backend
5. Configure static file serving
6. Set up SSL/HTTPS

### Docker Deployment (Optional)

Create `docker-compose.yml` for containerized deployment with PostgreSQL and Redis.

## Customization

### Templates

- Modify templates in `templates/` directory
- Customize email templates in `templates/*/email/`
- Update CSS in `static/css/style.css`

### Models

- Extend `Subscriber` model for additional fields
- Add custom fields to `Newsletter` model
- Create custom admin actions

### Email Templates

- HTML templates in `templates/newsletter/email/`
- Customize styling and branding
- Add dynamic content

## Troubleshooting

### Common Issues

1. **Email not sending**: Check email configuration and credentials
2. **Celery not working**: Ensure Redis is running
3. **Database errors**: Verify PostgreSQL connection
4. **Static files not loading**: Run `collectstatic` command

### Logs

- Django logs: Check console output
- Celery logs: Monitor worker terminal
- Email logs: Check email backend logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test
4. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:
- Check the troubleshooting section
- Review Django and Celery documentation
- Create an issue in the repository
