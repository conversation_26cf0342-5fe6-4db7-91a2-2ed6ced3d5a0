from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from .models import Article, Category, Comment
import json


def blog_home(request):
    """Blog home page with featured articles and ad placements"""
    featured_articles = Article.objects.filter(status='featured')[:3]
    recent_articles = Article.objects.filter(status__in=['published', 'featured'])[:6]
    categories = Category.objects.all()[:8]
    
    context = {
        'featured_articles': featured_articles,
        'recent_articles': recent_articles,
        'categories': categories,
        'page_title': 'The Idea Engine - Creative Inspiration & Innovation',
        'meta_description': 'Discover fresh ideas, creative inspiration, and innovative insights. Join The Idea Engine community for actionable tips and breakthrough thinking.',
    }
    return render(request, 'blog/home.html', context)


def article_list(request):
    """List all published articles with pagination"""
    articles = Article.objects.filter(status__in=['published', 'featured'])
    
    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        articles = articles.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(meta_keywords__icontains=search_query)
        )
    
    # Category filter
    category_slug = request.GET.get('category')
    if category_slug:
        articles = articles.filter(category__slug=category_slug)
    
    # Pagination
    paginator = Paginator(articles, 9)  # 9 articles per page for ad optimization
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    categories = Category.objects.all()
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_slug,
        'page_title': 'Articles - The Idea Engine',
        'meta_description': 'Browse our collection of creative articles, innovation insights, and actionable tips for entrepreneurs and creative professionals.',
    }
    return render(request, 'blog/article_list.html', context)


def article_detail(request, slug):
    """Article detail page with ad placements"""
    article = get_object_or_404(Article, slug=slug, status__in=['published', 'featured'])
    
    # Increment views for analytics
    article.increment_views()
    
    # Get related articles
    related_articles = Article.objects.filter(
        category=article.category,
        status__in=['published', 'featured']
    ).exclude(id=article.id)[:3]
    
    # Get approved comments
    comments = article.comments.filter(is_approved=True)
    
    context = {
        'article': article,
        'related_articles': related_articles,
        'comments': comments,
        'page_title': article.title,
        'meta_description': article.meta_description or article.excerpt,
        'meta_keywords': article.meta_keywords,
    }
    return render(request, 'blog/article_detail.html', context)


def category_detail(request, slug):
    """Category page with articles"""
    category = get_object_or_404(Category, slug=slug)
    articles = Article.objects.filter(
        category=category,
        status__in=['published', 'featured']
    )
    
    # Pagination
    paginator = Paginator(articles, 9)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'category': category,
        'page_obj': page_obj,
        'page_title': f'{category.name} - The Idea Engine',
        'meta_description': category.description or f'Explore {category.name} articles on The Idea Engine.',
    }
    return render(request, 'blog/category_detail.html', context)


@csrf_exempt
def add_comment(request, article_slug):
    """Add comment to article (AJAX)"""
    if request.method == 'POST':
        try:
            article = get_object_or_404(Article, slug=article_slug)
            data = json.loads(request.body)
            
            comment = Comment.objects.create(
                article=article,
                name=data.get('name'),
                email=data.get('email'),
                content=data.get('content')
            )
            
            return JsonResponse({
                'success': True,
                'message': 'Comment submitted successfully! It will be reviewed before publishing.'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': 'Error submitting comment. Please try again.'
            })
    
    return JsonResponse({'success': False, 'message': 'Invalid request method'})


def search(request):
    """Search articles"""
    query = request.GET.get('q', '')
    articles = []
    
    if query:
        articles = Article.objects.filter(
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(meta_keywords__icontains=query),
            status__in=['published', 'featured']
        )
    
    # Pagination
    paginator = Paginator(articles, 9)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'query': query,
        'page_title': f'Search Results for "{query}" - The Idea Engine',
        'meta_description': f'Search results for {query} on The Idea Engine.',
    }
    return render(request, 'blog/search_results.html', context)
