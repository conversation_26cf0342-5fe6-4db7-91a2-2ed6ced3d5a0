# Generated by Django 5.1.4 on 2025-06-17 09:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
                ('excerpt', models.TextField(help_text='Brief description for SEO and previews', max_length=300)),
                ('content', models.TextField(help_text='Full article content - HTML allowed')),
                ('featured_image', models.URLField(blank=True, help_text='URL to featured image')),
                ('meta_description', models.CharField(blank=True, help_text='SEO meta description', max_length=160)),
                ('meta_keywords', models.CharField(blank=True, help_text='SEO keywords (comma separated)', max_length=200)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('featured', 'Featured')], default='draft', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('views', models.PositiveIntegerField(default=0)),
                ('reading_time', models.PositiveIntegerField(default=5, help_text='Estimated reading time in minutes')),
                ('enable_ads', models.BooleanField(default=True, help_text='Enable Google AdSense on this article')),
                ('ad_placement', models.CharField(choices=[('standard', 'Standard (Top, Middle, Bottom)'), ('minimal', 'Minimal (Top and Bottom only)'), ('aggressive', 'Aggressive (Multiple placements)')], default='standard', max_length=20)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='articles', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='articles', to='blog.category')),
            ],
            options={
                'ordering': ['-published_at', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_approved', models.BooleanField(default=False)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='blog.article')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
