from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from django.utils.text import slugify


class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('blog:category', kwargs={'slug': self.slug})


class Article(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('featured', 'Featured'),
    ]

    title = models.Char<PERSON>ield(max_length=200)
    slug = models.SlugField(max_length=200, unique=True, blank=True)
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='articles')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='articles')
    excerpt = models.TextField(max_length=300, help_text="Brief description for SEO and previews")
    content = models.TextField(help_text="Full article content - HTML allowed")
    featured_image = models.URLField(blank=True, help_text="URL to featured image")
    
    # SEO fields
    meta_description = models.CharField(max_length=160, blank=True, help_text="SEO meta description")
    meta_keywords = models.CharField(max_length=200, blank=True, help_text="SEO keywords (comma separated)")
    
    # Status and dates
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)
    
    # Engagement metrics
    views = models.PositiveIntegerField(default=0)
    reading_time = models.PositiveIntegerField(default=5, help_text="Estimated reading time in minutes")
    
    # AdSense optimization
    enable_ads = models.BooleanField(default=True, help_text="Enable Google AdSense on this article")
    ad_placement = models.CharField(
        max_length=20,
        choices=[
            ('standard', 'Standard (Top, Middle, Bottom)'),
            ('minimal', 'Minimal (Top and Bottom only)'),
            ('aggressive', 'Aggressive (Multiple placements)'),
        ],
        default='standard'
    )

    class Meta:
        ordering = ['-published_at', '-created_at']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        
        # Set published_at when status changes to published
        if self.status in ['published', 'featured'] and not self.published_at:
            self.published_at = timezone.now()
        
        # Auto-generate meta description from excerpt if not provided
        if not self.meta_description and self.excerpt:
            self.meta_description = self.excerpt[:160]
            
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('blog:article_detail', kwargs={'slug': self.slug})

    def increment_views(self):
        """Increment article views for analytics"""
        self.views += 1
        self.save(update_fields=['views'])

    @property
    def is_published(self):
        return self.status in ['published', 'featured']

    @property
    def estimated_ad_revenue(self):
        """Estimate potential ad revenue based on views"""
        # Rough estimate: $1-3 per 1000 views
        return round((self.views / 1000) * 2, 2)


class Comment(models.Model):
    article = models.ForeignKey(Article, on_delete=models.CASCADE, related_name='comments')
    name = models.CharField(max_length=100)
    email = models.EmailField()
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_approved = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f'Comment by {self.name} on {self.article.title}'
