from django.contrib import admin
from django.utils.html import format_html
from .models import Category, Article, Comment


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'article_count', 'created_at']
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ['name', 'description']

    def article_count(self, obj):
        return obj.articles.filter(status__in=['published', 'featured']).count()
    article_count.short_description = 'Published Articles'


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'author', 'category', 'status', 'views', 
        'estimated_revenue', 'published_at', 'enable_ads'
    ]
    list_filter = ['status', 'category', 'enable_ads', 'ad_placement', 'created_at']
    search_fields = ['title', 'content', 'meta_keywords']
    prepopulated_fields = {'slug': ('title',)}
    date_hierarchy = 'published_at'
    
    fieldsets = (
        ('Article Content', {
            'fields': ('title', 'slug', 'author', 'category', 'excerpt', 'content', 'featured_image')
        }),
        ('SEO Optimization', {
            'fields': ('meta_description', 'meta_keywords', 'reading_time'),
            'classes': ('collapse',)
        }),
        ('Publishing', {
            'fields': ('status', 'published_at')
        }),
        ('AdSense Settings', {
            'fields': ('enable_ads', 'ad_placement'),
            'classes': ('collapse',)
        }),
        ('Analytics', {
            'fields': ('views',),
            'classes': ('collapse',)
        })
    )
    
    readonly_fields = ['views']
    
    def estimated_revenue(self, obj):
        revenue = obj.estimated_ad_revenue
        if revenue > 0:
            return format_html('<span style="color: green;">${}</span>', revenue)
        return '$0.00'
    estimated_revenue.short_description = 'Est. Ad Revenue'
    
    actions = ['make_published', 'make_featured', 'enable_ads', 'disable_ads']
    
    def make_published(self, request, queryset):
        queryset.update(status='published')
    make_published.short_description = "Mark selected articles as published"
    
    def make_featured(self, request, queryset):
        queryset.update(status='featured')
    make_featured.short_description = "Mark selected articles as featured"
    
    def enable_ads(self, request, queryset):
        queryset.update(enable_ads=True)
    enable_ads.short_description = "Enable ads on selected articles"
    
    def disable_ads(self, request, queryset):
        queryset.update(enable_ads=False)
    disable_ads.short_description = "Disable ads on selected articles"


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ['name', 'article', 'created_at', 'is_approved']
    list_filter = ['is_approved', 'created_at']
    search_fields = ['name', 'email', 'content']
    actions = ['approve_comments']
    
    def approve_comments(self, request, queryset):
        queryset.update(is_approved=True)
    approve_comments.short_description = "Approve selected comments"
